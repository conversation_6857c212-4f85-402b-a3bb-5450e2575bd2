# ChessGameViewModel Architecture Optimization Plan

## 🔍 Current Architecture Issues

### Data Flow Problems Identified:

1. **Double ObservableObject Chain Problem**:
   ```swift
   ChessGameViewModel (@ObservedObject) 
   ├── session: GameSession (@ObservedObject)
   ├── dragManager: ChessDragManager (@ObservedObject)
   ├── moveExecutor: ChessMoveExecutor (@ObservedObject)
   ├── navigator: ChessNavigator (@ObservedObject)
   └── moveDisplayManager: MoveDisplayManager (@ObservedObject)
   ```
   This creates nested observation chains causing performance issues and redundant updates.

2. **Manual objectWillChange.send() Overuse**: 
   - 30+ manual `objectWillChange.send()` calls across ChessGameViewModel and GameSession
   - Manual callback chains instead of reactive data flow

3. **Mixed Responsibility Patterns**:
   - ChessGameViewModel still holds UI state that belongs to specific modules
   - Computed properties that should be reactive published properties

4. **Complex Callback Web**:
   - Multiple callback setup methods creating tight coupling
   - Callbacks triggering other callbacks creating potential cycles

## 🏗️ Proposed Architecture: Event-Driven Design

### Core Strategy: Event-Driven Architecture with Clean Data Flow

Replace callback-heavy approach with cleaner event-driven architecture.

### 1. Central Event Bus Pattern
```swift
@MainActor
class ChessGameEventBus: ObservableObject {
    @Published var events: [ChessGameEvent] = []
    
    func publish(_ event: ChessGameEvent) {
        events.append(event)
        // Auto-remove after processing to prevent memory issues
    }
}

enum ChessGameEvent {
    case moveCompleted(Move)
    case promotionNeeded(Move)
    case variationCreationNeeded(Move, MoveTree.MoveIndex, MoveTree.MoveIndex?)
    case navigationStateChanged
    case boardStateChanged
    case selectionChanged(Square?)
    case cacheInvalidationNeeded
    // ... other events
}
```

### 2. Simplified Module Architecture
```swift
// Convert @ObservedObject modules to pure service classes
@MainActor
final class ChessGameViewModel: ObservableObject {
    // Core state only
    @Published var session: GameSession
    @Published var uiState: ChessGameUIState
    
    // Services (not @ObservedObject)
    private let eventBus = ChessGameEventBus()
    private let dragService: ChessDragService
    private let moveService: ChessMoveService
    private let navigationService: ChessNavigationService
    private let displayService: MoveDisplayService
    
    // Single event subscription
    private var eventSubscription: AnyCancellable?
}

struct ChessGameUIState {
    var lastMove: Move?
    var selectedSquare: Square?
    var possibleMoves: [Square]
    var showPromotionDialog: Bool
    var promotionMove: Move?
    var showVariationCreationDialog: Bool
    var pendingMove: Move?
    var pendingMoveFromIndex: MoveTree.MoveIndex?
    var existingNextMoveIndex: MoveTree.MoveIndex?
    var showMoveEditMenu: Bool
    var editMenuPosition: CGPoint
    var selectedMoveForEdit: MoveTree.MoveIndex?
    // ... all UI state consolidated
}
```

### 3. Reactive Service Design
```swift
// Example: ChessNavigationService (not @ObservableObject)
@MainActor
class ChessNavigationService {
    private let eventBus: ChessGameEventBus
    
    // Navigation state (accessed via ViewModel)
    var showVariationSelection: Bool = false
    var availableVariations: [VariationOption] = []
    var isKeyboardNavigationDisabled: Bool = false
    
    init(eventBus: ChessGameEventBus) {
        self.eventBus = eventBus
    }
    
    // Clean methods without callbacks
    func goToNextMove(in session: GameSession) {
        // Navigation logic here
        eventBus.publish(.navigationStateChanged)
    }
    
    func selectVariation(_ option: VariationOption, in session: GameSession) {
        // Variation selection logic
        showVariationSelection = false
        availableVariations = []
        isKeyboardNavigationDisabled = false
        session.goToMove(at: option.index)
        eventBus.publish(.navigationStateChanged)
    }
}
```

### 4. View Layer Optimization
```swift
// Simplified view access through ViewModel
// Instead of: viewModel.navigator.goToNextMove(in: viewModel.session)
// Use: viewModel.goToNextMove() 
// Which internally uses: navigationService.goToNextMove(in: session)
```

## 📋 Implementation Plan

### Phase 1: Event Bus Foundation
**Goal**: Establish event-driven communication infrastructure

1. **Create ChessGameEventBus**
   - Implement event bus with proper event lifecycle management
   - Define comprehensive ChessGameEvent enum
   - Add event batching for performance

2. **Create ChessGameUIState**
   - Consolidate all UI-related @Published properties
   - Remove scattered UI state from ViewModel
   - Implement proper state updates via events

3. **Update ChessGameViewModel**
   - Replace callback setup with single event subscription
   - Integrate event bus and UI state
   - Maintain existing public interface for compatibility

### Phase 2: Service Layer Transformation
**Goal**: Convert @ObservedObject modules to pure service classes

1. **Transform ChessDragManager → ChessDragService**
   - Remove @ObservableObject conformance
   - Replace @Published properties with direct state access
   - Use event bus for state change notifications

2. **Transform ChessMoveExecutor → ChessMoveService**
   - Convert callback-based architecture to event publishing
   - Simplify move execution logic
   - Remove coupling with ViewModel

3. **Transform ChessNavigator → ChessNavigationService**
   - Remove @ObservableObject and @Published properties
   - Implement event-based navigation state changes
   - Maintain navigation logic integrity

4. **Transform MoveDisplayManager → MoveDisplayService**
   - Convert to service pattern
   - Use events for cache invalidation
   - Optimize move display performance

### Phase 3: Integration and Cleanup
**Goal**: Complete integration and remove obsolete code

1. **Update View Layer**
   - Modify views to use simplified ViewModel interface
   - Remove direct module access patterns
   - Update binding patterns to use uiState

2. **Remove Manual ObjectWillChange Calls**
   - Eliminate 30+ manual objectWillChange.send() calls
   - Replace with proper reactive updates
   - Clean up redundant update notifications

3. **Optimize Performance**
   - Remove nested ObservableObject chains
   - Implement efficient event processing
   - Add performance monitoring for updates

4. **Update Tests**
   - Modify tests to work with new architecture
   - Add event bus testing utilities
   - Ensure full test coverage maintained

### Phase 4: Advanced Optimizations
**Goal**: Performance and maintainability improvements

1. **Event Batching**
   - Implement event coalescing for rapid updates
   - Add debouncing for expensive operations
   - Optimize UI update frequency

2. **Service Interface Standardization**
   - Create consistent service interfaces
   - Add service lifecycle management
   - Implement dependency injection patterns

3. **Memory Management**
   - Optimize event lifecycle
   - Remove potential retain cycles
   - Add proper cleanup in deinit

## 🎯 Expected Benefits

### Performance Improvements
- **Eliminate nested ObservableObject chains**: Remove redundant update cascades
- **Reduce manual objectWillChange calls**: From 30+ to near zero
- **Optimize update frequency**: Event batching reduces UI churn
- **Better memory management**: Clear event lifecycle prevents memory leaks

### Architecture Benefits
- **Clear separation of concerns**: Each service handles one responsibility
- **Loose coupling**: Event-driven communication reduces dependencies
- **Better testability**: Services can be easily mocked and tested independently
- **Improved maintainability**: Clear event flow instead of callback webs
- **Enhanced debugging**: Event logs provide clear system behavior visibility

### Developer Experience
- **Simplified view code**: Clean ViewModel interface
- **Consistent patterns**: Unified service architecture across modules
- **Better error handling**: Centralized error event handling
- **Easier feature addition**: New modules integrate via event bus

## 🔧 Implementation Notes

### Compatibility Strategy
- Maintain existing public ViewModel interface during transition
- Use feature flags for gradual rollout
- Ensure all tests pass at each phase
- Document breaking changes clearly

### Risk Mitigation
- Implement comprehensive logging for event flow
- Add performance benchmarks to detect regressions
- Create rollback plan for each phase
- Maintain parallel callback system during transition if needed

### Quality Assurance
- All existing tests must pass
- No functional regressions allowed
- Performance must improve or remain equivalent
- Memory usage should decrease

This architecture transformation will create a cleaner, more maintainable, and better-performing chess game system while preserving all existing functionality.