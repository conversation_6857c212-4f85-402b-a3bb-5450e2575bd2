refactor: Remove backward compatibility wrappers from ChessGameViewModel after module refactoring

## Major Code Cleanup Following Architecture Refactoring

### Backward Compatibility Code Removal
- **Eliminated redundant wrapper methods**: Removed 40+ lines of backward compatibility code that was preserved during the module refactoring process
- **Direct module access**: Views and tests now interact directly with specialized service modules instead of going through ChessGameViewModel wrappers
- **Cleaner architecture**: ChessGameViewModel is now focused purely on UI state management without duplicate navigation logic

### Removed Navigation Wrapper Methods
- **Core navigation methods**: `goToPreviousMove()`, `goToNextMove()`, `goToStart()`, `goToEnd()`, `goToMove(at:)`
- **Variation selection wrappers**: `selectVariation()`, `cancelVariationSelection()`
- **Navigation capability properties**: `canGoToPreviousMove`, `canGoToNextMove`

### Updated Direct Module Usage Across Codebase

#### View Layer Updates
- **InteractiveMoveNotationView**: Updated all navigation buttons to use `viewModel.navigator.*` methods directly with session parameter
- **VariationSelectionView**: Updated variation selection to call `navigator.selectVariation(option, in: session)` directly
- **ChessView**: Updated keyboard shortcuts to call navigator methods with proper session context
- **ChessGameView**: Updated variation cancellation to use `navigator.cancelVariationSelection()` directly

#### Comprehensive Test Suite Updates
- **175+ test method calls updated**: All navigation-related test calls now use navigator API directly
- **Navigation capability assertions**: Modified to use `navigator.canGoToPreviousMove(in: session)` pattern
- **Full test coverage maintained**: All existing tests pass with direct module access
- **Consistent API usage**: Tests now follow the same direct module access pattern as the views

### Technical Benefits Achieved

#### Architecture Improvements
- **Better separation of concerns**: Navigation logic entirely handled by ChessNavigator service
- **Reduced code duplication**: Eliminated duplicate code paths between wrapper methods and actual implementations
- **Improved maintainability**: Single source of truth for navigation functionality
- **Enhanced clarity**: Direct module dependencies make code relationships explicit

#### Code Quality Improvements
- **Eliminated indirection**: Removed unnecessary method call layers
- **Consistent API usage**: All components now use the same direct module access pattern
- **Reduced complexity**: ChessGameViewModel is now simpler and more focused
- **Better testability**: Direct module access simplifies testing and debugging

### Verification and Quality Assurance
- **All tests pass successfully**: Comprehensive test suite validates functionality preservation
- **Navigation behavior preserved**: All existing navigation functionality works identically
- **No regressions introduced**: UI behavior remains exactly the same for end users
- **Clean build**: No compilation warnings or errors

### Completion of Module Refactoring
This change marks the completion of the architecture refactoring that began with the extraction of specialized service modules. The temporary backward compatibility layer has been successfully removed, resulting in:

- **Clean module boundaries**: Clear separation between UI, navigation, move execution, and other concerns
- **Direct dependencies**: Components directly depend on the modules they need
- **Simplified codebase**: Elimination of redundant wrapper code
- **Modern architecture**: Following established patterns for modular design